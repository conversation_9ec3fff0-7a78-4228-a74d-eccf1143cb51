---
type: 'agent_requested'
description: 'BuilderBot technical implementation rules and requirements'
---

# 🔧 BuilderBot Technical Rules

## ⚠️ MANDATORY Requirements

## 1️⃣ Core Architecture

### Required Components

```typescript
import {
  createBot,
  createProvider,
  createFlow,
  MemoryDB,
} from '@builderbot/bot';

const main = async () => {
  const adapterDB = new MemoryDB(); // or production DB
  const adapterFlow = createFlow([
    /* flows */
  ]);
  const adapterProvider = createProvider(/* provider */);

  const { handleCtx, httpServer } = await createBot({
    flow: adapterFlow, // MANDATORY
    provider: adapterProvider, // MANDATORY
    database: adapterDB, // MANDATORY
  });
};
```

### ❌ FORBIDDEN

- Creating bots without all three components
- Using deprecated bot-whatsapp imports
- Mixing BuilderBot with other WhatsApp libraries

## 2️⃣ Flow Rules

### Required Structure

```typescript
const flow = addKeyword(['trigger'])
  .addAnswer('Response message')
  .addAction(async (ctx, { flowDynamic }) => {
    // Action logic here
  });
```

### Keywords

```typescript
addKeyword(['hello', 'hi', 'start']); // Multiple triggers
addKeyword('hello'); // Single trigger
addKeyword(REGEX_PATTERN, { regex: true }); // Regex with flag
```

### Answers

```typescript
.addAnswer('Message text')
.addAnswer(['Multi', 'line', 'message'])
.addAnswer('Message with media', { media: 'https://...' })
.addAnswer('Delayed message', { delay: 2000 })
```

## 3️⃣ Provider Rules

### Production

```typescript
import { MetaProvider } from '@builderbot/provider-meta';

const adapterProvider = createProvider(MetaProvider, {
  jwtToken: process.env.META_JWT_TOKEN,
  numberId: process.env.META_NUMBER_ID,
  verifyToken: process.env.META_VERIFY_TOKEN,
  version: 'v16.0',
});
```

### Development

```typescript
import { BaileysProvider } from '@builderbot/provider-baileys';
const adapterProvider = createProvider(BaileysProvider);

// Environment separation
const provider =
  process.env.NODE_ENV === 'production' ? MetaProvider : BaileysProvider;
```

## 4️⃣ Database Rules

### Production

```typescript
import { MongoAdapter } from '@builderbot/database-mongo';

const adapterDB = new MongoAdapter({
  dbUri: process.env.MONGO_URI,
  dbName: process.env.MONGO_DB,
});
```

### Development

```typescript
import { MemoryDB } from '@builderbot/bot';
const adapterDB = new MemoryDB();

// Environment-based selection
const adapterDB =
  process.env.NODE_ENV === 'production'
    ? new MongoAdapter(config)
    : new MemoryDB();
```

## 5️⃣ Error Handling

```typescript
// Try-catch in async operations
.addAction(async (ctx, { flowDynamic }) => {
    try {
        const result = await externalAPI.call()
        await flowDynamic(`Result: ${result}`)
    } catch (error) {
        console.error('Action error:', error)
        await flowDynamic('Sorry, there was an error. Please try again.')
    }
})

// Global error handling
process.on('unhandledRejection', (reason, promise) => {
    console.error('Unhandled Rejection at:', promise, 'reason:', reason)
})
```

### ❌ FORBIDDEN

- Unhandled promise rejections
- Silent error swallowing
- Exposing internal errors to users

## 6️⃣ State Management

```typescript
.addAction({ capture: true }, async (ctx, { state, flowDynamic }) => {
    // Update state properly
    await state.update({ userInput: ctx.body })

    // Retrieve state safely
    const currentState = state.getMyState()

    // Validate state data
    if (!currentState?.userInput) {
        await flowDynamic('Please provide valid input')
        return
    }
})
```

### ❌ FORBIDDEN

- Direct state mutation
- Storing sensitive data in state
- State without validation

## 7️⃣ Security

```typescript
// Environment variables for secrets
const config = {
  jwtToken: process.env.META_JWT_TOKEN,
  apiKey: process.env.EXTERNAL_API_KEY,
  dbPassword: process.env.DB_PASSWORD,
};

// Input validation
const validatePhoneNumber = (phone: string): boolean => {
  const phoneRegex = /^\+[1-9]\d{1,14}$/;
  return phoneRegex.test(phone);
};

// Sanitize user input
const sanitizeInput = (input: string): string => {
  return input.trim().replace(/[<>]/g, '');
};
```

### ❌ FORBIDDEN

- Hardcoded credentials
- Plain text passwords
- Exposing internal system information
- Missing input validation

## 8️⃣ Performance

```typescript
// Connection pooling
const adapterDB = new MongoAdapter({
  dbUri: process.env.MONGO_URI,
  maxPoolSize: 10,
  minPoolSize: 2,
  maxIdleTimeMS: 30000,
});

// Timeout configurations
const httpTimeout = 10000; // 10 seconds max
const MAX_CONCURRENT_FLOWS = 100;
```

## 9️⃣ Logging

```typescript
import { logger } from './utils/logger'

.addAction(async (ctx, { flowDynamic }) => {
    logger.info('User action', {
        userId: ctx.from,
        action: 'button_click',
        timestamp: new Date().toISOString()
    })
})

// Error logging
catch (error) {
    logger.error('Flow error', {
        error: error.message,
        userId: ctx.from,
        flow: 'checkout'
    })
}
```

## 🔟 Deployment

```typescript
// Environment validation
const requiredEnvVars = [
  'META_JWT_TOKEN',
  'META_NUMBER_ID',
  'META_VERIFY_TOKEN',
  'MONGO_URI',
  'NODE_ENV',
];

requiredEnvVars.forEach((envVar) => {
  if (!process.env[envVar]) {
    throw new Error(`Missing required environment variable: ${envVar}`);
  }
});

// Health check endpoint
adapterProvider.server.get('/health', (req, res) => {
  res.json({
    status: 'healthy',
    timestamp: new Date().toISOString(),
  });
});
```

## ✅ Checklist

- [ ] All three core components configured
- [ ] Production database (not MemoryDB)
- [ ] Production provider (Meta/Twilio)
- [ ] Environment variables set
- [ ] Error handling implemented
- [ ] Input validation implemented
- [ ] Logging configured
- [ ] Security measures implemented

## 🚨 Critical Rules

1. **Never use MemoryDB in production**
2. **Always use environment variables**
3. **Implement proper error handling**
4. **Validate all user inputs**
