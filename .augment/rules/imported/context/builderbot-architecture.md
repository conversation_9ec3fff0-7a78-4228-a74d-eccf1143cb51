---
type: 'agent_requested'
description: 'BuilderBot framework architecture and core concepts'
---

# 🏗️ BuilderBot Architecture

## 📖 Overview

BuilderBot is an open-source framework for creating chatbots that connect to messaging platforms like WhatsApp, Telegram, and others.

## 🎯 Core Architecture

BuilderBot uses a **modular architecture** with three fundamental components:

1. **Flow** - Conversation logic and user interactions
2. **Provider** - Communication with messaging platforms
3. **Database** - Data persistence and user state

Benefits:

- Platform-agnostic conversation logic
- Independent component scaling
- Isolated testing capabilities

## 🧩 Core Components

### 1. Flow (Conversation Engine)

Defines bot responses and conversation sequences.

```typescript
const welcomeFlow = addKeyword(['hello', 'hi'])
  .addAnswer('Welcome! How can I help you?')
  .addAnswer('Choose an option:', {
    buttons: [{ body: 'Products' }, { body: 'Support' }],
  });
```

**Core Methods:**

- `addKeyword()`: Define trigger words
- `addAnswer()`: Send messages
- `addAction()`: Execute custom logic
- `gotoFlow()`: Navigate between flows

### 2. Provider (Communication Layer)

Bridge between BuilderBot and messaging platforms.

```typescript
const adapterProvider = createProvider(BaileysProvider);
// or for production
const adapterProvider = createProvider(MetaProvider, {
  jwtToken: 'your_token',
  numberId: 'your_number_id',
});
```

**Available Providers:**

- **Meta**: Official API, production use
- **Twilio**: Official API, enterprise
- **Baileys**: Unofficial, development/testing
- **Venom**: Unofficial, small scale
- **WPPConnect**: Unofficial, prototyping

### 3. Database (Persistence Layer)

Manages data persistence and user sessions.

```typescript
const adapterDB = new MemoryDB(); // Development
// or for production
const adapterDB = new MongoAdapter({
  dbUri: 'mongodb://localhost:27017',
  dbName: 'builderbot',
});
```

**Available Adapters:**

- **MemoryDB**: Development/testing (no persistence)
- **MongoDB**: Production NoSQL (high scalability)
- **MySQL**: Production relational
- **PostgreSQL**: Production advanced features

## 🔄 Request Flow

1. **Message Reception**: Provider receives message
2. **Keyword Matching**: Flow engine matches keywords
3. **Flow Selection**: Appropriate flow selected
4. **Context Loading**: User state loaded from database
5. **Flow Execution**: Selected flow executes
6. **State Updates**: Changes persisted to database
7. **Response Generation**: Flow generates responses
8. **Message Sending**: Provider sends responses back

## 🎛️ Configuration

```typescript
export const config = {
  provider: {
    type: process.env.PROVIDER_TYPE || 'baileys',
    meta: {
      jwtToken: process.env.META_JWT_TOKEN,
      numberId: process.env.META_NUMBER_ID,
      verifyToken: process.env.META_VERIFY_TOKEN,
    },
  },
  database: {
    type: process.env.DB_TYPE || 'memory',
    mongo: {
      uri: process.env.MONGO_URI,
      dbName: process.env.MONGO_DB_NAME,
    },
  },
  app: {
    port: process.env.PORT || 3000,
    environment: process.env.NODE_ENV || 'development',
  },
};
```

## 🔌 Plugins

Available plugins for extending functionality:

- **Telegram**: Telegram platform support
- **Shopify**: E-commerce integration
- **Agents**: AI agent capabilities
- **Langchain**: Advanced AI integration

```typescript
const bot = createBot({
  flow: adapterFlow,
  provider: adapterProvider,
  database: adapterDB,
  plugins: [TelegramPlugin, ShopifyPlugin.configure({...})],
});
```

## 🚀 Deployment

### Development Setup

```typescript
const adapterDB = new MemoryDB();
const adapterProvider = createProvider(BaileysProvider);
const adapterFlow = createFlow([welcomeFlow]);

const bot = createBot({
  flow: adapterFlow,
  provider: adapterProvider,
  database: adapterDB,
});
```

### Production Setup

```typescript
const adapterDB = new MongoAdapter({
  dbUri: process.env.MONGO_URI,
  dbName: process.env.MONGO_DB_NAME,
});

const adapterProvider = createProvider(MetaProvider, {
  jwtToken: process.env.META_JWT_TOKEN,
  numberId: process.env.META_NUMBER_ID,
});

const { httpServer } = await createBot({
  flow: adapterFlow,
  provider: adapterProvider,
  database: adapterDB,
});

httpServer(process.env.PORT || 3000);
```

## 📊 Key Features

### Event Monitoring

```typescript
bot.on('message_received', (ctx) => {
  logger.info('Message received', { from: ctx.from });
});
```

### Session Management

```typescript
const sessionConfig = {
  timeout: 30 * 60 * 1000, // 30 minutes
  maxSessions: 10000,
};
```

### Scaling

- Use cluster module for horizontal scaling
- MongoDB replica sets for database scaling
- Redis for caching and session management
