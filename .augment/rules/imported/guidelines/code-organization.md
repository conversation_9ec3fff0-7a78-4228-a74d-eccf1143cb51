---
type: 'agent_requested'
description: 'Example description'
---

# 📁 Code Organization Guidelines

## 🎯 Overview

This document provides guidelines for organizing BuilderBot projects to ensure maintainability, scalability, and team collaboration.

## 📂 Recommended Project Structure

```
botsito_backend/
├── src/
│   ├── app.ts                    # Main application entry point
│   ├── config/
│   │   ├── index.ts              # Configuration management
│   │   ├── database.ts           # Database configuration
│   │   ├── providers.ts          # Provider configurations
│   │   └── environment.ts        # Environment validation
│   ├── flows/
│   │   ├── index.ts              # Flow exports
│   │   ├── welcome/
│   │   │   ├── welcome.flow.ts   # Welcome flow logic
│   │   │   └── welcome.test.ts   # Welcome flow tests
│   │   ├── products/
│   │   │   ├── products.flow.ts
│   │   │   ├── categories.flow.ts
│   │   │   └── search.flow.ts
│   │   ├── orders/
│   │   │   ├── checkout.flow.ts
│   │   │   ├── tracking.flow.ts
│   │   │   └── returns.flow.ts
│   │   └── support/
│   │       ├── faq.flow.ts
│   │       └── human-handoff.flow.ts
│   ├── services/
│   │   ├── database.service.ts   # Database operations
│   │   ├── payment.service.ts    # Payment processing
│   │   ├── inventory.service.ts  # Inventory management
│   │   ├── notification.service.ts # Notifications
│   │   └── analytics.service.ts  # Analytics tracking
│   ├── middleware/
│   │   ├── auth.middleware.ts    # Authentication
│   │   ├── rate-limit.middleware.ts # Rate limiting
│   │   ├── logging.middleware.ts # Request logging
│   │   └── validation.middleware.ts # Input validation
│   ├── utils/
│   │   ├── logger.ts             # Logging utilities
│   │   ├── validators.ts         # Input validators
│   │   ├── formatters.ts         # Message formatters
│   │   ├── cache.ts              # Caching utilities
│   │   └── helpers.ts            # General helpers
│   ├── types/
│   │   ├── index.ts              # Type exports
│   │   ├── user.types.ts         # User-related types
│   │   ├── order.types.ts        # Order-related types
│   │   └── flow.types.ts         # Flow-related types
│   └── constants/
│       ├── messages.ts           # Static messages
│       ├── errors.ts             # Error messages
│       └── config.ts             # Application constants
├── docs/                         # Documentation
├── tests/
│   ├── unit/                     # Unit tests
│   ├── integration/              # Integration tests
│   └── e2e/                      # End-to-end tests
├── scripts/
│   ├── deploy.sh                 # Deployment scripts
│   ├── backup.sh                 # Backup scripts
│   └── migrate.ts                # Database migrations
├── .env.example                  # Environment template
├── .gitignore                    # Git ignore rules
├── package.json                  # Dependencies
├── tsconfig.json                 # TypeScript config
├── Dockerfile                    # Container config
└── README.md                     # Project documentation
```

## 🌊 Flow Organization

### 1. Modular Flow Structure

```typescript
// flows/welcome/welcome.flow.ts
import { addKeyword } from '@builderbot/bot';
import { BaileysProvider } from '@builderbot/provider-baileys';
import { MemoryDB } from '@builderbot/bot';

export const welcomeFlow = addKeyword<BaileysProvider, MemoryDB>([
  'hello',
  'hi',
  'start',
])
  .addAnswer(['👋 Welcome to our store!', 'How can I help you today?'])
  .addAnswer([
    'Choose an option:',
    '1️⃣ Browse products',
    '2️⃣ Check order status',
    '3️⃣ Contact support',
    '4️⃣ Account settings',
  ]);

// flows/products/products.flow.ts
export const productsFlow = addKeyword(['1', 'products', 'browse', 'catalog'])
  .addAnswer('🛍️ Product Categories:')
  .addAnswer([
    'A) Electronics 📱',
    'B) Clothing 👕',
    'C) Home & Garden 🏠',
    'D) Books 📚',
  ]);

// flows/index.ts
export { welcomeFlow } from './welcome/welcome.flow';
export { productsFlow } from './products/products.flow';
export { checkoutFlow } from './orders/checkout.flow';
export { supportFlow } from './support/faq.flow';
```

### 2. Flow Naming Conventions

```typescript
// RECOMMENDED: Descriptive flow names
const welcomeFlow = addKeyword(['hello']);
const productCatalogFlow = addKeyword(['products']);
const orderTrackingFlow = addKeyword(['track']);
const customerSupportFlow = addKeyword(['support']);

// AVOID: Generic or unclear names
const flow1 = addKeyword(['hello']);
const mainFlow = addKeyword(['products']);
const userFlow = addKeyword(['track']);
```

## 🔧 Service Layer Organization

### 1. Service Structure

```typescript
// services/database.service.ts
export class DatabaseService {
  async getUserById(userId: string): Promise<User | null> {
    // Database logic
  }

  async saveUserPreferences(
    userId: string,
    preferences: UserPreferences
  ): Promise<void> {
    // Save logic
  }

  async getOrderHistory(userId: string): Promise<Order[]> {
    // Order retrieval logic
  }
}

// services/payment.service.ts
export class PaymentService {
  async processPayment(paymentData: PaymentRequest): Promise<PaymentResult> {
    // Payment processing logic
  }

  async refundPayment(orderId: string): Promise<RefundResult> {
    // Refund logic
  }
}

// services/index.ts
export { DatabaseService } from './database.service';
export { PaymentService } from './payment.service';
export { InventoryService } from './inventory.service';
```

### 2. Service Injection Pattern

```typescript
// config/services.ts
import { DatabaseService, PaymentService, InventoryService } from '../services';

export const services = {
  database: new DatabaseService(),
  payment: new PaymentService(),
  inventory: new InventoryService(),
};

// flows/orders/checkout.flow.ts
import { services } from '../../config/services';

export const checkoutFlow = addKeyword(['checkout']).addAction(
  async (ctx, { flowDynamic, state }) => {
    try {
      const user = await services.database.getUserById(ctx.from);
      const cart = await services.inventory.getCart(ctx.from);

      // Process checkout logic
    } catch (error) {
      await flowDynamic('Sorry, checkout is temporarily unavailable.');
    }
  }
);
```

## 📝 Configuration Management

### 1. Environment-Based Configuration

```typescript
// config/environment.ts
import { z } from 'zod';

const envSchema = z.object({
  NODE_ENV: z.enum(['development', 'staging', 'production']),
  PORT: z.string().transform(Number),

  // Database
  MONGO_URI: z.string().url(),
  MONGO_DB_NAME: z.string(),

  // WhatsApp Provider
  META_JWT_TOKEN: z.string().optional(),
  META_NUMBER_ID: z.string().optional(),
  META_VERIFY_TOKEN: z.string().optional(),

  // External APIs
  PAYMENT_API_KEY: z.string(),
  INVENTORY_API_URL: z.string().url(),
});

export const env = envSchema.parse(process.env);

// config/index.ts
import { env } from './environment';

export const config = {
  app: {
    port: env.PORT,
    environment: env.NODE_ENV,
    isDevelopment: env.NODE_ENV === 'development',
    isProduction: env.NODE_ENV === 'production',
  },

  database: {
    uri: env.MONGO_URI,
    name: env.MONGO_DB_NAME,
  },

  providers: {
    meta: {
      jwtToken: env.META_JWT_TOKEN,
      numberId: env.META_NUMBER_ID,
      verifyToken: env.META_VERIFY_TOKEN,
    },
  },

  apis: {
    payment: {
      apiKey: env.PAYMENT_API_KEY,
    },
    inventory: {
      baseUrl: env.INVENTORY_API_URL,
    },
  },
};
```

## 🎯 Type Definitions

### 1. Comprehensive Type System

```typescript
// types/user.types.ts
export interface User {
  id: string;
  phoneNumber: string;
  name?: string;
  email?: string;
  preferences: UserPreferences;
  createdAt: Date;
  lastActivity: Date;
}

export interface UserPreferences {
  language: 'en' | 'es' | 'fr';
  notifications: boolean;
  currency: 'USD' | 'EUR' | 'MXN';
}

// types/order.types.ts
export interface Order {
  id: string;
  userId: string;
  items: OrderItem[];
  total: number;
  status: OrderStatus;
  createdAt: Date;
  updatedAt: Date;
}

export interface OrderItem {
  productId: string;
  name: string;
  quantity: number;
  price: number;
}

export enum OrderStatus {
  PENDING = 'pending',
  CONFIRMED = 'confirmed',
  SHIPPED = 'shipped',
  DELIVERED = 'delivered',
  CANCELLED = 'cancelled',
}

// types/flow.types.ts
export interface FlowContext {
  userId: string;
  currentFlow: string;
  state: Record<string, any>;
  lastMessage: Date;
}
```

## 🧪 Testing Organization

### 1. Test Structure

```typescript
// tests/unit/flows/welcome.flow.test.ts
import { welcomeFlow } from '../../../src/flows/welcome/welcome.flow';

describe('Welcome Flow', () => {
  test('should respond to hello keyword', async () => {
    const mockCtx = { from: 'test_user', body: 'hello' };
    const mockUtils = { flowDynamic: jest.fn() };

    await welcomeFlow.handler(mockCtx, mockUtils);

    expect(mockUtils.flowDynamic).toHaveBeenCalledWith(
      expect.stringContaining('Welcome')
    );
  });
});

// tests/integration/services/database.service.test.ts
import { DatabaseService } from '../../../src/services/database.service';

describe('Database Service Integration', () => {
  let databaseService: DatabaseService;

  beforeEach(() => {
    databaseService = new DatabaseService();
  });

  test('should save and retrieve user', async () => {
    const user = await databaseService.createUser({
      phoneNumber: '+1234567890',
      name: 'Test User',
    });

    const retrieved = await databaseService.getUserById(user.id);
    expect(retrieved).toEqual(user);
  });
});
```

## 📊 Logging and Monitoring

### 1. Structured Logging

```typescript
// utils/logger.ts
import winston from 'winston'

export const logger = winston.createLogger({
    level: process.env.LOG_LEVEL || 'info',
    format: winston.format.combine(
        winston.format.timestamp(),
        winston.format.errors({ stack: true }),
        winston.format.json()
    ),
    transports: [
        new winston.transports.Console(),
        new winston.transports.File({ filename: 'logs/error.log', level: 'error' }),
        new winston.transports.File({ filename: 'logs/combined.log' })
    ]
})

// Usage in flows
import { logger } from '../../utils/logger'

.addAction(async (ctx, { flowDynamic }) => {
    logger.info('User action', {
        userId: ctx.from,
        action: 'product_search',
        query: ctx.body
    })
})
```

## 🔄 Deployment Organization

### 1. Environment-Specific Configurations

```typescript
// config/database.ts
import { config } from './index';

export const getDatabaseConfig = () => {
  if (config.app.isProduction) {
    return {
      uri: config.database.uri,
      options: {
        maxPoolSize: 10,
        minPoolSize: 2,
        maxIdleTimeMS: 30000,
      },
    };
  }

  return {
    uri: 'mongodb://localhost:27017/builderbot_dev',
    options: {
      maxPoolSize: 5,
      minPoolSize: 1,
    },
  };
};
```

## ✅ Code Organization Checklist

### Project Structure

- [ ] Clear separation of concerns (flows, services, utils)
- [ ] Consistent naming conventions
- [ ] Proper TypeScript types defined
- [ ] Environment configuration properly structured

### Flow Organization

- [ ] Flows grouped by functionality
- [ ] Each flow in its own file
- [ ] Clear flow naming conventions
- [ ] Proper imports and exports

### Service Layer

- [ ] Business logic separated from flows
- [ ] Services properly injected
- [ ] Error handling implemented
- [ ] Proper abstraction levels

### Testing

- [ ] Unit tests for individual components
- [ ] Integration tests for services
- [ ] E2E tests for critical flows
- [ ] Test coverage above 80%

### Documentation

- [ ] README with setup instructions
- [ ] API documentation
- [ ] Flow documentation
- [ ] Deployment guides

**Remember: Good organization is an investment in your future self and your team. Take time to structure your code properly from the beginning.**
